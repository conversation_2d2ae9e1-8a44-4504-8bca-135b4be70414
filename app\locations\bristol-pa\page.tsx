import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"

export const metadata = {
  title: "Yard Grading Services in Bristol, PA | 3S Builder Solution",
  description:
    "Professional yard grading, excavation, hardscaping, and landscaping services in Bristol, PA. Expert solutions for drainage issues and outdoor transformations.",
}

export default function BristolPage() {
  return (
    <div className="container mx-auto px-4 py-12 sm:px-6 lg:px-8">
      {/* Hero Section */}
      <div className="relative rounded-lg overflow-hidden mb-12">
        <div className="absolute inset-0 z-0">
          <Image
            src="/bristol_pa_hero__Hero_image_showing_professional.webp"
            alt="Professional yard grading, excavation, and landscaping services in Bristol, Pennsylvania"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-black bg-opacity-50"></div>
        </div>
        <div className="relative z-10 p-8 md:p-12 lg:p-16 text-white">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">
            Professional Yard Grading in Bristol, PA
          </h1>
          <p className="text-lg md:text-xl max-w-3xl mb-8">
            Transform your Bristol property with expert yard grading, excavation, hardscaping, and landscaping services.
            Serving the historic Delaware River community with professional outdoor solutions.
          </p>
          <Button asChild size="lg" className="bg-[#709CA7] hover:bg-[#4A6C6F]">
            <Link href="/contact-us">Get Your Free Bristol Estimate</Link>
          </Button>
        </div>
      </div>

      {/* Services We Offer */}
      <div className="mb-12">
        <h2 className="text-2xl md:text-3xl font-bold mb-6">Services We Offer in Bristol</h2>
        <div className="grid md:grid-cols-3 gap-8">
          {[
            {
              title: "Yard Grading",
              description:
                "Professional land grading services to ensure proper drainage and prepare your Bristol property for construction or landscaping projects.",
              image: "/bristol_yard_grading__Professional_yard_grading_e.webp",
              link: "/services/yard-grading",
            },
            {
              title: "Excavation",
              description:
                "Precise excavation services for foundations, drainage systems, and site preparation throughout Bristol and surrounding areas.",
              image: "/bristol_excavation__Excavation_services_in_Bristo.webp",
              link: "/services/excavation",
            },
            {
              title: "Hardscaping",
              description:
                "Beautiful and functional hardscape installations including patios, walkways, retaining walls, and outdoor kitchens for Bristol homes.",
              image: "/bristol_hardscaping__Beautiful_hardscaping_instal.webp",
              link: "/services/hardscaping",
            },
            {
              title: "Landscaping",
              description:
                "Complete landscaping services including sod installation, planting, mulching, and landscape design to beautify Bristol outdoor spaces.",
              image: "/bristol_landscaping__Complete_landscaping_project.webp",
              link: "/services/landscaping",
            },
          ].map((service, index) => (
            <Link key={index} href={service.link} className="group">
              <div className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                <div className="relative h-48">
                  <Image
                    src={service.image || "/placeholder.svg"}
                    alt={`Professional ${service.title.toLowerCase()} services in Bristol, PA by 3S Builder Solution`}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-2 group-hover:text-[#4A6C6F] transition-colors">{service.title}</h3>
                  <p className="text-gray-700 mb-4">{service.description}</p>
                  <span className="text-[#4A6C6F] hover:text-[#3a5a5d] font-medium">
                    Learn more about our {service.title.toLowerCase()} services →
                  </span>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* Why Choose Us */}
      <div className="mb-12 bg-[#F3F1E7] rounded-lg p-8">
        <h2 className="text-2xl md:text-3xl font-bold mb-6">Why Bristol Chooses 3S Builder Solution</h2>
        <div className="grid md:grid-cols-2 gap-8">
          <div>
            <ul className="space-y-4">
              {[
                {
                  title: "Local Bristol Expertise",
                  description:
                    "We understand Bristol's unique soil conditions, proximity to the Delaware River, and local regulations, allowing us to provide tailored solutions for your specific needs.",
                },
                {
                  title: "Drainage Solutions",
                  description:
                    "Bristol's location near the Delaware River can create unique drainage challenges. Our team specializes in effective drainage solutions for waterfront and low-lying properties.",
                },
                {
                  title: "Historic Property Experience",
                  description:
                    "Bristol's rich history means many properties have unique characteristics. We have experience working with historic homes and properties with special considerations.",
                },
                {
                  title: "Professional Equipment",
                  description:
                    "We invest in professional-grade equipment to ensure efficient, precise work on projects of any size in Bristol's diverse neighborhoods.",
                },
                {
                  title: "Free Estimates",
                  description:
                    "We provide detailed, no-obligation estimates so you know exactly what to expect before any work begins on your Bristol property.",
                },
                {
                  title: "Customer Satisfaction Focus",
                  description:
                    "Your satisfaction is our priority. We work closely with you throughout the project to ensure the results meet your expectations.",
                },
              ].map((item, index) => (
                <li key={index} className="flex">
                  <div className="flex-shrink-0 h-6 w-6 mt-1 mr-3 flex items-center justify-center rounded-full bg-[#709CA7]/20 text-[#4A6C6F]">
                    <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-lg font-medium">{item.title}</h4>
                    <p className="text-gray-600">{item.description}</p>
                  </div>
                </li>
              ))}
            </ul>
          </div>
          <div className="relative rounded-lg overflow-hidden h-[500px]">
            <Image
              src="/bristol_why_choose_us__3S_Builder_Solution_team_w.webp"
              alt="3S Builder Solution professional yard grading and landscaping project in Bristol, PA showcasing quality workmanship"
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>

      {/* Featured Projects - Placeholder */}
      <div className="mb-12">
        <h2 className="text-2xl md:text-3xl font-bold mb-6">Featured Projects in Bristol</h2>
        <div className="grid md:grid-cols-2 gap-8">
          {[
            {
              title: "Delaware River Property Drainage Solution",
              description:
                "Professional yard grading and drainage system installation that solved persistent water issues for a residential property near the Delaware River.",
              image: "/bristol_drainage_project__Before_and_after_or_in.webp",
            },
            {
              title: "Historic Home Landscape Transformation",
              description:
                "Complete yard grading, hardscaping, and landscaping project that enhanced a historic Bristol home while preserving its character.",
              image: "/bristol_historic_landscape__Landscaping_project_a.webp",
            },
          ].map((project, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="relative h-64">
                <Image
                  src={project.image || "/placeholder.svg"}
                  alt={`${project.title} - Professional outdoor services project by 3S Builder Solution in Bristol, PA`}
                  fill
                  className="object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-2">{project.title}</h3>
                <p className="text-gray-700">{project.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>



      {/* Areas We Serve */}
      <div className="mb-12">
        <h2 className="text-2xl md:text-3xl font-bold mb-4">Serving Bristol and Surrounding Areas</h2>
        <p className="text-lg text-gray-700 mb-6">
          3S Builder Solution provides services throughout Bristol and nearby communities. Our service area includes:
        </p>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <div className="bg-[#4A6C6F] p-3 rounded-lg text-center font-bold text-white">Bristol, PA</div>
          {["Bensalem", "Levittown", "Morrisville", "Langhorne", "Newtown", "Feasterville", "Southampton"].map(
            (location, index) => (
              <div key={index} className="bg-[#F3F1E7] p-3 rounded-lg text-center text-gray-700">
                {location}, PA
              </div>
            ),
          )}
        </div>
        <div className="mt-6 text-center">
          <Button asChild variant="outline" className="text-[#4A6C6F] border-[#4A6C6F] hover:bg-[#F3F1E7]">
            <Link href="/locations">View All Service Areas</Link>
          </Button>
        </div>
      </div>

      {/* CTA */}
      <div className="bg-[#4A6C6F] rounded-lg p-8 text-white text-center">
        <h2 className="text-2xl md:text-3xl font-bold mb-4">Ready to Transform Your Bristol Property?</h2>
        <p className="text-lg mb-6 max-w-3xl mx-auto">
          Contact 3S Builder Solution today for a free consultation and estimate. Our professional team is ready to help
          with your yard grading, hardscaping, and landscaping needs in Bristol.
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Button asChild size="lg" className="bg-white text-[#4A6C6F] hover:bg-[#F3F1E7]">
            <Link href="/contact-us">Get Your Free Estimate in Bristol</Link>
          </Button>
          <Button asChild size="lg" className="bg-white text-[#4A6C6F] hover:bg-[#F3F1E7] border-2 border-white">
            <a href="tel:4132414577" className="flex items-center gap-2">
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                />
              </svg>
              <span>************</span>
            </a>
          </Button>
        </div>
      </div>
    </div>
  )
}
