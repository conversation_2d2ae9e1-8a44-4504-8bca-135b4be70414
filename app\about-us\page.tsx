import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"

export const metadata = {
  title: "About Us | 3S Builder Solution",
  description:
  "Learn about 3S Builder Solution - your trusted partner for professional yard grading, excavation, land leveling, hardscaping, and landscaping services in Southeastern PA.",
}

export default function AboutPage() {
  return (
    <div className="container mx-auto px-4 py-12 sm:px-6 lg:px-8">
      {/* Hero Section */}
      <div className="relative rounded-lg overflow-hidden mb-12">
        <div className="absolute inset-0 z-0">
          <Image
            src="/About_3S_Builder_Solution__Your_trusted_partner_for_professional_outdoor_services_with_over_5_years.webp"
            alt="3S Builder Solution professional team providing expert outdoor services with over 5 years of experience in Southeastern Pennsylvania"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-black bg-opacity-50"></div>
        </div>
        <div className="relative z-10 p-8 md:p-12 lg:p-16 text-white">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">About 3S Builder Solution</h1>
          <p className="text-lg md:text-xl max-w-3xl mb-8">
            Your trusted partner for professional outdoor services with over 5 years of experience transforming
            properties across Southeastern Pennsylvania.
          </p>
        </div>
      </div>

      {/* Our Story */}
      <div className="mb-16">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-2xl md:text-3xl font-bold mb-6">Our Story</h2>
            <div className="space-y-4 text-lg text-gray-700">
              <p>
                3S Builder Solution was founded with a simple mission: to provide high-quality, professional outdoor
                services to homeowners and businesses throughout Pennsylvania. With years of experience in
                the industry, we&apos;ve built a reputation for excellence in yard grading, excavation, land leveling, hardscaping, and
                landscaping.
              </p>
              <p>
                What began as a small operation has grown into a trusted service provider throughout Southeastern
                Pennsylvania. Our growth has been driven by our commitment to quality workmanship, attention to detail,
                and customer satisfaction.
              </p>
              <p>
                Today, we continue to serve homeowners and businesses across the region, bringing our expertise and
                professional equipment to every project. We take pride in our ability to solve complex outdoor
                challenges and create beautiful, functional spaces that our clients love.
              </p>
            </div>
          </div>
          <div className="relative h-[400px] rounded-lg overflow-hidden">
            <Image
              src="/Our_Story__Our_Story_(1).webp"
              alt="3S Builder Solution company history and growth in providing professional outdoor services across Southeastern Pennsylvania"
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>

      {/* Our Mission */}
      <div className="mb-16 bg-gray-50 rounded-lg p-8">
        <div className="text-center max-w-3xl mx-auto">
          <h2 className="text-2xl md:text-3xl font-bold mb-6">Our Mission</h2>
          <p className="text-xl text-gray-700 italic">
            &quot;To deliver exceptional outdoor transformation services with integrity, expertise, and attention to detail,
            creating beautiful, functional spaces that enhance our clients&apos; properties and lives.&quot;
          </p>
        </div>
      </div>

      {/* Our Values */}
      <div className="mb-16">
        <h2 className="text-2xl md:text-3xl font-bold mb-8 text-center">Our Core Values</h2>
        <div className="grid md:grid-cols-3 gap-8">
          {[
            {
              title: "Quality",
              description:
                "We never compromise on quality. From materials to workmanship, we ensure everything meets our high standards.",
              icon: "/Our_Core_Values__Quality.webp",
            },
            {
              title: "Integrity",
              description:
                "We operate with honesty and transparency in all our dealings, providing fair pricing and clear communication.",
              icon: "/Our_Core_Values__Integrity.webp",
            },
            {
              title: "Expertise",
              description:
                "Our team brings specialized knowledge and skills to every project, ensuring professional results.",
              icon: "/Our_Core_Values__Expertise.webp",
            },
            {
              title: "Attention to Detail",
              description:
                "We believe the small details make a big difference in the final result and durability of our work.",
              icon: "/Our_Core_Values__Attention_to_Detail.webp",
            },
            {
              title: "Customer Focus",
              description:
                "We listen to our clients' needs and preferences, working collaboratively to achieve their vision.",
              icon: "/Our_Core_Values__Customer_Focus.webp",
            },
            {
              title: "Reliability",
              description: "We honor our commitments, showing up on time and completing projects as promised.",
              icon: "/Our_Core_Values__Reliability.webp",
            },
          ].map((value, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm p-6 text-center">
              <div className="flex justify-center mb-4">
                <Image
                  src={value.icon || "/placeholder.svg"}
                  alt={`${value.title} icon representing 3S Builder Solution's commitment to ${value.title.toLowerCase()} in outdoor services`}
                  width={60}
                  height={60}
                  className="rounded-full bg-[#709CA7]/20 p-2"
                />
              </div>
              <h3 className="text-xl font-bold mb-2">{value.title}</h3>
              <p className="text-gray-700">{value.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Our Expertise */}
      <div className="mb-16">
        <h2 className="text-2xl md:text-3xl font-bold mb-8">Our Expertise</h2>
        <div className="grid md:grid-cols-2 gap-8">
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="p-6">
              <h3 className="text-xl font-bold mb-4">Professional Services</h3>
              <ul className="space-y-3">
                {[
                  {
                    service: "Yard Grading",
                    description: "Expert yard grading for proper drainage and foundation stability.",
                  },
                  {
                    service: "Excavation",
                    description: "Precise excavation services for various construction and landscaping needs.",
                  },
                  {
                    service: "Land Leveling",
                    description: "Ground land leveling to create perfect bases for outdoor features.",
                  },
                  {
                    service: "Hardscaping",
                    description: "Beautiful hardscape installations including patios, walkways, and retaining walls.",
                  },
                  {
                    service: "Landscaping",
                    description: "Complete landscaping services including sod, planting, and mulching.",
                  },
                ].map((item, index) => (
                  <li key={index} className="flex">
                    <div className="flex-shrink-0 h-6 w-6 mt-1 mr-3 flex items-center justify-center rounded-full bg-[#709CA7]/20 text-green-600">
                      <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-medium">{item.service}</h4>
                      <p className="text-gray-600">{item.description}</p>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="p-6">
              <h3 className="text-xl font-bold mb-4">Professional Equipment</h3>
              <p className="text-gray-700 mb-4">
                We invest in professional-grade equipment to ensure efficient, precise work on every project. Our
                equipment includes:
              </p>
              <ul className="space-y-3">
                {[
                  "Bobcat Skid Steers for versatile material handling",
                  "Mini Excavators for precise excavation and yard grading",
                  "Laser Levels for perfectly even grades and slopes",
                  "Compactors for proper soil and base preparation",
                  "Professional landscaping tools for quality finishing work",
                ].map((item, index) => (
                  <li key={index} className="flex items-center">
                    <div className="flex-shrink-0 h-5 w-5 mr-3 text-green-600">
                      <svg fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <span className="text-gray-700">{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Service Areas */}
      <div className="mb-16 bg-gray-50 rounded-lg p-8">
        <h2 className="text-2xl md:text-3xl font-bold mb-6 text-center">Areas We Serve</h2>
        <p className="text-lg text-gray-700 mb-6 text-center max-w-3xl mx-auto">
          3S Builder Solution proudly serves communities throughout Southeastern Pennsylvania, bringing our expertise to
          homeowners and businesses across the region.
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          {[
            "Philadelphia",
            "Bensalem",
            "Abington",
            "Warminster",
            "Mount Airy",
            "Horsham",
            "Springfield Township",
            "Lansdale",
            "Southampton",
            "Willow Grove",
            "Chestnut Hill",
            "Bristol",
            "Hatboro",
          ].map((location, index) => (
            <Link
              key={index}
              href={`/locations/${location.toLowerCase().replace(" ", "-")}-pa`}
              className="px-4 py-2 bg-white rounded-full text-[#4A6C6F] hover:bg-[#F3F1E7] border border-[#709CA7]"
            >
              {location}, PA
            </Link>
          ))}
        </div>
        <div className="mt-8 text-center">
          <Button asChild className="bg-[#4A6C6F] hover:bg-[#3a5a5d]">
            <Link href="/locations">View All Service Areas</Link>
          </Button>
        </div>
      </div>

      {/* CTA */}
      <div className="bg-[#4A6C6F] rounded-lg p-8 text-white text-center">
        <h2 className="text-2xl md:text-3xl font-bold mb-4">Ready to Work With Us?</h2>
        <p className="text-lg mb-6 max-w-3xl mx-auto">
          Contact 3S Builder Solution today to discuss your project needs and get a free estimate. We look forward to
          helping you transform your outdoor space.
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Button asChild size="lg" className="bg-white text-[#4A6C6F] hover:bg-[#F3F1E7]">
            <Link href="/contact-us">Contact Us Today</Link>
          </Button>
          <Button asChild size="lg" className="bg-white text-[#4A6C6F] hover:bg-[#F3F1E7] border-2 border-white">
            <a href="tel:4132414577" className="flex items-center gap-2">
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                />
              </svg>
              <span>************</span>
            </a>
          </Button>
        </div>
      </div>
    </div>
  )
}
