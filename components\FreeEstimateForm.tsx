"use client";

import { useEffect, useRef, useState } from 'react';

// Type definition for the OpnForm window object
interface OpnFormWindow extends Window {
  initEmbed?: (formId: string) => void;
}

declare const window: OpnFormWindow;

const FreeEstimateForm = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    // Validate the form URL before loading
    const formId = 'free-estimate-form-afcau9';
    const allowedDomain = 'opnform.com';

    // Check if the script has already been added to avoid duplicates
    if (document.getElementById('opnform-widget-script')) {
      // If script exists, just ensure the iframe is initialized if needed
      try {
        if (typeof window.initEmbed === 'function') {
          window.initEmbed(formId);
        }
        setIsLoading(false);
      } catch (error) {
        console.error('Failed to initialize existing OpnForm embed:', error);
        setHasError(true);
        setIsLoading(false);
      }
      return;
    }

    const script = document.createElement('script');
    script.id = 'opnform-widget-script';
    script.type = 'text/javascript';
    script.src = `https://${allowedDomain}/widgets/iframe.min.js`;
    script.crossOrigin = 'anonymous';
    script.onload = () => {
      try {
        // Initialize the embed after the script loads
        if (typeof window.initEmbed === 'function') {
          window.initEmbed(formId);
        }
        setIsLoading(false);
      } catch (error) {
        // Handle any errors during embed initialization
        console.error('Failed to initialize OpnForm embed:', error);
        setHasError(true);
        setIsLoading(false);
      }
    };

    script.onerror = (error) => {
      console.error('Failed to load OpnForm script:', error);
      setHasError(true);
      setIsLoading(false);
    };
    script.async = true; // Load script asynchronously

    // Append the script to the body or a specific element
    // Appending to body is generally safer for third-party scripts
    document.body.appendChild(script);

    return () => {
      // Clean up the script when the component unmounts
      const scriptElement = document.getElementById('opnform-widget-script');
      if (scriptElement) {
        scriptElement.remove();
      }
    };
  }, []); // Empty dependency array ensures this runs once on mount and cleans up on unmount

  return (
    <div ref={containerRef}>
      {isLoading && (
        <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#4A6C6F] mx-auto mb-4"></div>
            <p className="text-gray-600">Loading contact form...</p>
          </div>
        </div>
      )}

      {hasError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <p className="text-red-700 mb-4">Unable to load the contact form. Please try refreshing the page or contact us directly.</p>
          <div className="space-y-2">
            <p className="font-semibold text-gray-900">Contact Information:</p>
            <p className="text-gray-700">Phone: <a href="tel:************" className="text-[#4A6C6F] hover:underline">************</a></p>
            <p className="text-gray-700">Email: <a href="mailto:<EMAIL>" className="text-[#4A6C6F] hover:underline"><EMAIL></a></p>
          </div>
        </div>
      )}

      {!hasError && (
        <iframe
          style={{
            border: 'none',
            width: '100%',
            height: '700px',
            display: isLoading ? 'none' : 'block'
          }}
          id="free-estimate-form-afcau9"
          src="https://opnform.com/forms/free-estimate-form-afcau9"
          title="Free Estimate Form"
          sandbox="allow-scripts allow-forms allow-same-origin"
          loading="lazy"
        />
      )}
    </div>
  );
};

export default FreeEstimateForm;