"use client";

import { useEffect, useRef } from 'react';

// Type definition for the OpnForm window object
interface OpnFormWindow extends Window {
  initEmbed?: (formId: string) => void;
}

declare const window: OpnFormWindow;

const FreeEstimateForm = () => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Check if the script has already been added to avoid duplicates
    if (document.getElementById('opnform-widget-script')) {
      // If script exists, just ensure the iframe is initialized if needed
      try {
        if (typeof window.initEmbed === 'function') {
          window.initEmbed('free-estimate-form-afcau9');
        }
      } catch (error) {
        console.error('Failed to initialize existing OpnForm embed:', error);
      }
      return;
    }

    const script = document.createElement('script');
    script.id = 'opnform-widget-script';
    script.type = 'text/javascript';
    script.src = 'https://opnform.com/widgets/iframe.min.js';
    script.onload = () => {
      try {
        // Initialize the embed after the script loads
        if (typeof window.initEmbed === 'function') {
          window.initEmbed('free-estimate-form-afcau9');
        }
      } catch (error) {
        // Handle any errors during embed initialization
        console.error('Failed to initialize OpnForm embed:', error);
      }
    };
    
    script.onerror = (error) => {
      console.error('Failed to load OpnForm script:', error);
    };
    script.async = true; // Load script asynchronously

    // Append the script to the body or a specific element
    // Appending to body is generally safer for third-party scripts
    document.body.appendChild(script);

    return () => {
      // Clean up the script when the component unmounts
      const scriptElement = document.getElementById('opnform-widget-script');
      if (scriptElement) {
        scriptElement.remove();
      }
    };
  }, []); // Empty dependency array ensures this runs once on mount and cleans up on unmount

  return (
    <div ref={containerRef}>
      {/* The iframe will be created and managed by the script */}
      {/* We can add a placeholder or loading indicator here if needed */}
      <iframe
        style={{ border: 'none', width: '100%', height: '700px' }}
        id="free-estimate-form-afcau9"
        src="https://opnform.com/forms/free-estimate-form-afcau9"
        title="Free Estimate Form" // Added title for accessibility
      ></iframe>
    </div>
  );
};

export default FreeEstimateForm;