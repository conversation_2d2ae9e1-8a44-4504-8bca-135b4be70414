import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"

export const metadata = {
  title: "Yard Grading Services in Willow Grove, PA | 3S Builder Solution",
  description:
    "Professional yard grading, excavation, land leveling, hardscaping, and landscaping services in Willow Grove, PA. Free estimates available from 3S Builder Solution.",
}

export default function WillowGrovePage() {
  return (
    <div className="container mx-auto px-4 py-12 sm:px-6 lg:px-8">
      {/* Hero Section */}
      <div className="relative rounded-lg overflow-hidden mb-12">
        <div className="absolute inset-0 z-0">
          <Image
            src="/Expert_Grading_Hardscaping__Landscaping_in_Willow_Grove_PA__Transforming_Willow_Grove_properties.webp"
            alt="Willow Grove, PA area"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-black bg-opacity-50"></div>
        </div>
        <div className="relative z-10 p-8 md:p-12 lg:p-16 text-white">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">
            Professional Outdoor Services in Willow Grove, PA
          </h1>
          <p className="text-lg md:text-xl max-w-3xl mb-8">
            Transforming Willow Grove properties with professional outdoor services and attention to detail.
          </p>
          <Button asChild size="lg" className="bg-[#4A6C6F] hover:bg-[#3a5a5d]">
            <Link href="/contact-us">Get Your Free Estimate in Willow Grove</Link>
          </Button>
        </div>
      </div>

      {/* Introduction */}
      <div className="mb-12">
        <h2 className="text-2xl md:text-3xl font-bold mb-4">3S Builder Solution in Willow Grove</h2>
        <p className="text-lg text-gray-700 mb-4">
          Welcome to 3S Builder Solution, your trusted partner for professional outdoor services in Willow Grove, PA. We
          understand the unique challenges and opportunities that come with Willow Grove properties, from suburban homes to
          commercial spaces.
        </p>
        <p className="text-lg text-gray-700">
          Our team brings over 5 years of experience, professional equipment, and meticulous attention to detail to
          every project in Willow Grove. Whether you need proper yard grading for drainage solutions, beautiful hardscaping for
          outdoor living, or expert landscaping to enhance your property&apos;s appearance, we deliver quality results that
          stand the test of time.
        </p>
      </div>

      {/* Services Offered */}
      <div className="mb-12">
        <h2 className="text-2xl md:text-3xl font-bold mb-6">Services We Offer in Willow Grove</h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {[
            {
              title: "Yard Grading",
              description:
                "Professional land yard grading services to ensure proper drainage and prevent water damage to your Willow Grove property.",
              image: "/Services_We_Offer_in_Willow_Grove__yard_grading.webp",
              link: "/services/yard-grading",
            },
            {
              title: "Excavation",
              description:
                "Precise excavation services for foundations, utilities, drainage systems, and more throughout Willow Grove.",
              image: "/Services_We_Offer_in_Willow_Grove__Digging.webp",
              link: "/services/excavation",
            },
            {
              title: "Land Leveling",
              description:
                "Expert ground land leveling to create the perfect base for patios, walkways, and other outdoor features for Willow Grove homes and businesses.",
              image: "/Services_We_Offer_in_Willow_Grove__lawn_leveling.webp",
              link: "/services/land-leveling",
            },
            {
              title: "Hardscaping",
              description:
                "Beautiful and durable hardscape installations including patios, walkways, retaining walls, and more for Willow Grove properties.",
              image: "/Services_We_Offer_in_Willow_Grove__Hardscaping.webp",
              link: "/services/hardscaping",
            },
            {
              title: "Landscaping",
              description:
                "Complete landscaping services including sod installation, planting, mulching, and landscape design to beautify Willow Grove outdoor spaces.",
              image: "/Services_We_Offer_in_Willow_Grove__Landscaping.webp",
              link: "/services/landscaping",
            },
          ].map((service, index) => (
            <Link key={index} href={service.link} className="group">
              <div className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-lg transition-shadow h-full">
              <div className="relative h-48">
                <Image
                  src={service.image || "/placeholder.svg"}
                  alt={`${service.title} services in Willow Grove`}
                  fill
                  className="object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-2 group-hover:text-[#4A6C6F] transition-colors">{service.title}</h3>
                <p className="text-gray-700 mb-4">{service.description}</p>
                <span className="text-[#4A6C6F] hover:text-[#3a5a5d] font-medium">
                    Learn more about our {service.title.toLowerCase()} services →
                  </span>
              </div>
            </div>
            </Link>
          ))}
        </div>
      </div>

      {/* Why Choose Us */}
      <div className="mb-12 bg-[#F3F1E7] rounded-lg p-8">
        <h2 className="text-2xl md:text-3xl font-bold mb-6">Why Willow Grove Chooses 3S Builder Solution</h2>
        <div className="grid md:grid-cols-2 gap-8">
          <div>
            <ul className="space-y-4">
              {[
                {
                  title: "Local Expertise",
                  description:
                    "We understand Willow Grove's unique soil conditions, regulations, and property types, allowing us to provide tailored solutions for your specific needs.",
                },
                {
                  title: "5+ Years of Experience",
                  description:
                    "Our team brings years of professional experience to every project, ensuring quality workmanship and reliable results.",
                },
                {
                  title: "Attention to Detail",
                  description:
                    "We take pride in our meticulous approach, paying close attention to every aspect of your project for a flawless finish.",
                },
                {
                  title: "Professional Equipment",
                  description:
                    "We invest in professional-grade equipment to ensure efficient, precise work on projects of any size.",
                },
                {
                  title: "Free Estimates",
                  description:
                    "We provide detailed, no-obligation estimates so you know exactly what to expect before any work begins.",
                },
                {
                  title: "Customer Satisfaction Focus",
                  description:
                    "Your satisfaction is our priority. We work closely with you throughout the project to ensure the results meet your expectations.",
                },
              ].map((item, index) => (
                <li key={index} className="flex">
                  <div className="flex-shrink-0 h-6 w-6 mt-1 mr-3 flex items-center justify-center rounded-full bg-[#709CA7]/20 text-[#4A6C6F]">
                    <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-lg font-medium">{item.title}</h4>
                    <p className="text-gray-600">{item.description}</p>
                  </div>
                </li>
              ))}
            </ul>
          </div>
          <div className="relative rounded-lg overflow-hidden h-[500px]">
            <Image
              src="/Why_Willow_Grove_Chooses_3S_Builder_Solution__Willow_Grove_Project.webp"
              alt="3S Builder Solution project in Willow Grove"
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>

      {/* Featured Projects - Placeholder */}
      <div className="mb-12">
        <h2 className="text-2xl md:text-3xl font-bold mb-6">Featured Projects in Willow Grove</h2>
        <div className="grid md:grid-cols-2 gap-8">
          {[
            {
              title: "Backyard Transformation in Willow Grove",
              description:
                "Complete yard grading, hardscaping, and landscaping project that transformed a problematic sloped yard into a beautiful, functional outdoor living space.",
              image: "/Featured_Projects_in_Willow_Grove__Backyard_Transformation_in_Willow_Grove.webp",
            },
            {
              title: "Drainage Solution in Willow Grove",
              description:
                "Professional yard grading and drainage system installation that solved persistent water issues for a residential property.",
              image: "/Featured_Projects_in_Willow_Grove__Drainage_Solution_in_Willow_Grove.webp",
            },
          ].map((project, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="relative h-64">
                <Image src={project.image || "/placeholder.svg"} alt={project.title} fill className="object-cover" />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-2">{project.title}</h3>
                <p className="text-gray-700">{project.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}