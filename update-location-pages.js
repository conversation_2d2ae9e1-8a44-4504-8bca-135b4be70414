const fs = require('fs');
const path = require('path');

const locationsDir = path.join(__dirname, 'app', 'locations');

fs.readdirSync(locationsDir, { withFileTypes: true })
  .filter(d => d.isDirectory())
  .forEach(loc => {
    const locDir = path.join(locationsDir, loc.name);
    const pagePath = path.join(locDir, 'page.tsx');
    if (fs.existsSync(pagePath)) {
      let content = fs.readFileSync(pagePath, 'utf8');

      // Get city name from directory name
      const city = loc.name.replace('-pa', ', PA').replace(/-/g, ' ');

      // Capitalize first letter of each word in city name
      const cityCapitalized = city.replace(/\b\w/g, c => c.toUpperCase());

      // Update metadata title
      content = content.replace(
        /title: "Expert Grading, Hardscaping & Landscaping in [^,]+, PA \| 3S Builder Solution"/,
        `title: "Yard Grading Services in ${cityCapitalized} | 3S Builder Solution"`
      );

      // Update H1 tag
      content = content.replace(
        /Expert Grading, Hardscaping & Landscaping in [^,]+, PA/,
        `Professional Yard Grading in ${cityCapitalized}`
      );

      // Update service titles and descriptions
      content = content.replace(/Grading/g, 'Yard Grading');
      content = content.replace(/Leveling/g, 'Land Leveling');
      content = content.replace(/yard-grading/g, 'yard grading');
      content = content.replace(/land-leveling/g, 'land leveling');

      // Fix double "Yard Yard" issue
      content = content.replace(/Yard Yard/g, 'Yard');

      // Fix title issue
      content = content.replace(
        /title: "Yard Grading Services in [^,]+, PA \| 3S Builder Solution"/,
        `title: "Yard Grading Services in ${cityCapitalized} | 3S Builder Solution"`
      );

      // Fix H1 tag issue
      content = content.replace(
        /Professional Yard Grading in [^,]+, PA/,
        `Professional Yard Grading in ${cityCapitalized}`
      );

      // Fix lowercase city name in H1
      content = content.replace(
        /Professional Yard Grading in [a-z,]+, PA/,
        `Professional Yard Grading in ${cityCapitalized}`
      );

      fs.writeFileSync(pagePath, content, 'utf8');
      console.log(`Updated ${pagePath}`);
    }
  });