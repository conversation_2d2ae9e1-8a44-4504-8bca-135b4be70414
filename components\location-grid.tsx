import Link from "next/link"
import { Button } from "@/components/ui/button"

interface LocationGridProps {
  title?: string
  description?: string
  showViewAllButton?: boolean
  variant?: "default" | "compact"
}

const locations = [
  "Philadelphia",
  "Bensalem", 
  "Abington",
  "Warminster",
  "Mount Airy",
  "Horsham",
  "Springfield Township",
  "Lansdale",
  "Southampton",
  "Willow Grove",
  "Chestnut Hill",
  "Bristol",
  "Hatboro"
]

export default function LocationGrid({ 
  title = "Areas We Serve",
  description = "3S Builder Solution provides professional services throughout Southeastern Pennsylvania, including these primary service areas:",
  showViewAllButton = true,
  variant = "default"
}: LocationGridProps) {
  const containerClass = variant === "compact" 
    ? "mb-8" 
    : "mb-12 bg-[#F3F1E7] rounded-lg p-8"

  return (
    <div className={containerClass}>
      <h2 className="text-2xl md:text-3xl font-bold mb-4">{title}</h2>
      <p className="text-lg text-gray-700 mb-6">{description}</p>
      
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {locations.map((location, index) => (
          <Link
            key={index}
            href={`/locations/${location.toLowerCase().replace(" ", "-")}-pa`}
            className="bg-[#F3F1E7] p-3 rounded-lg text-center text-gray-700 hover:bg-[#4A6C6F] hover:text-white transition-colors"
          >
            {location}, PA
          </Link>
        ))}
      </div>
      
      {showViewAllButton && (
        <div className="mt-6 text-center">
          <Button asChild variant="outline" className="text-[#4A6C6F] border-[#4A6C6F] hover:bg-[#F3F1E7]">
            <Link href="/locations">View All Service Areas</Link>
          </Button>
        </div>
      )}
    </div>
  )
}
