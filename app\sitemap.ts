import { MetadataRoute } from 'next'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://www.3sbuildersolution.com'

  // Static pages
  const staticPages = [
    '',
    '/about-us',
    '/contact-us',
    '/services',
    '/projects',
    '/privacy-policy',
    '/terms-of-service',
    '/locations'
  ]

  // Service pages
  const servicePages = [
    '/services/yard-grading',
    '/services/excavation',
    '/services/land-leveling',
    '/services/hardscaping',
    '/services/landscaping'
  ]

  // Location pages
  const locationPages = [
    '/locations/abington-pa',
    '/locations/bensalem-pa',
    '/locations/bristol-pa',
    '/locations/chestnut-hill-pa',
    '/locations/hatboro-pa',
    '/locations/horsham-pa',
    '/locations/lansdale-pa',
    '/locations/mount-airy-pa',
    '/locations/philadelphia-pa',
    '/locations/southampton-pa',
    '/locations/springfield-township-pa',
    '/locations/warminster-pa',
    '/locations/willow-grove-pa'
  ]

  const allPages = [...staticPages, ...servicePages, ...locationPages]

  return allPages.map((page) => ({
    url: `${baseUrl}${page}`,
    lastModified: new Date(),
    changeFrequency: page === '' ? 'weekly' :
                    page.startsWith('/services') ? 'monthly' :
                    page.startsWith('/locations') ? 'monthly' : 'yearly',
    priority: page === '' ? 1 :
             page.startsWith('/services') ? 0.8 :
             page.startsWith('/locations') ? 0.7 : 0.5,
  }))
}
