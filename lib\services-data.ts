export interface Service {
  title: string
  description: string
  image: string
  link: string
  features?: string[]
}

export const services: Service[] = [
  {
    title: "Yard Grading",
    description: "Professional land yard grading services to ensure proper drainage and prevent water damage to your property.",
    image: "/Services_We_Offer_in_Mount_Airy__yard_grading.webp",
    link: "/services/yard-grading",
    features: [
      "Proper drainage solutions",
      "Foundation protection", 
      "Erosion prevention",
      "Site preparation"
    ]
  },
  {
    title: "Excavation",
    description: "Precise excavation services for foundations, utilities, drainage systems, and more.",
    image: "/Services_We_Offer_in_Mount_Airy__Digging.webp", 
    link: "/services/excavation",
    features: [
      "Foundation excavation",
      "Utility trenching",
      "Drainage systems",
      "Site clearing"
    ]
  },
  {
    title: "Land Leveling", 
    description: "Expert ground land leveling to create the perfect base for patios, walkways, and other outdoor features.",
    image: "/Services_We_Offer_in_Mount_Airy__lawn_leveling.webp",
    link: "/services/land-leveling",
    features: [
      "Precise leveling",
      "Base preparation",
      "Grade correction",
      "Surface smoothing"
    ]
  },
  {
    title: "Hardscaping",
    description: "Beautiful and durable hardscape installations including patios, walkways, retaining walls, and more.",
    image: "/Services_We_Offer_in_Mount_Airy__Hardscaping.webp",
    link: "/services/hardscaping", 
    features: [
      "Patio installation",
      "Walkway construction",
      "Retaining walls",
      "Outdoor living spaces"
    ]
  },
  {
    title: "Landscaping",
    description: "Complete landscaping services including sod installation, planting, mulching, and landscape design to beautify outdoor spaces.",
    image: "/Services_We_Offer_in_Mount_Airy__Landscaping.webp",
    link: "/services/landscaping",
    features: [
      "Sod installation",
      "Plant selection & installation",
      "Mulching services", 
      "Landscape design"
    ]
  }
]

export const getServicesByLocation = (location: string): Service[] => {
  return services.map(service => ({
    ...service,
    description: service.description.replace("your property", `your ${location} property`)
      .replace("outdoor spaces", `${location} outdoor spaces`)
  }))
}
