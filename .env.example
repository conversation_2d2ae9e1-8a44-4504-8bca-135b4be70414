# Environment Variables Example
# Copy this file to .env.local for local development
# Never commit actual .env files to version control

# Next.js Configuration
NEXT_PUBLIC_SITE_URL=https://www.3sbuildersolution.com
NEXT_PUBLIC_SITE_NAME="3S Builder Solution"

# Analytics (Optional)
# NEXT_PUBLIC_GA_ID=your-google-analytics-id
# NEXT_PUBLIC_GTM_ID=your-google-tag-manager-id

# Form Service Configuration
NEXT_PUBLIC_OPNFORM_ENDPOINT=https://opnform.com/forms/free-estimate-form-afcau9

# Security Configuration
# Set to 'production' in production environment
NODE_ENV=development

# Contact Information
NEXT_PUBLIC_PHONE=************
NEXT_PUBLIC_EMAIL=<EMAIL>

# Social Media (Optional)
# NEXT_PUBLIC_FACEBOOK_URL=
# NEXT_PUBLIC_INSTAGRAM_URL=
# NEXT_PUBLIC_LINKEDIN_URL=

# Vercel Configuration (for deployment)
# These are automatically set by Vercel
# VERCEL_URL=
# VERCEL_ENV=
