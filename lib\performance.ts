/**
 * Performance monitoring utilities for Core Web Vitals
 */

export interface WebVital {
  name: string
  value: number
  rating: 'good' | 'needs-improvement' | 'poor'
  delta: number
  id: string
}

/**
 * Report Core Web Vitals to console (can be extended to send to analytics)
 */
export function reportWebVitals(metric: WebVital) {
  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Performance] ${metric.name}:`, {
      value: metric.value,
      rating: metric.rating,
      delta: metric.delta,
      id: metric.id
    })
  }

  // In production, you could send to analytics service:
  // Example: Google Analytics, Vercel Analytics, etc.
  // gtag('event', metric.name, {
  //   value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
  //   event_label: metric.id,
  //   non_interaction: true,
  // })
}

/**
 * Performance thresholds for Core Web Vitals (2024 Google recommendations)
 * Updated to reflect current Core Web Vitals: LCP, INP, CLS
 * Plus additional performance metrics: FCP, TTFB
 */
export const PERFORMANCE_THRESHOLDS = {
  LCP: { good: 2500, poor: 4000 }, // Largest Contentful Paint (ms)
  INP: { good: 200, poor: 500 },   // Interaction to Next Paint (ms) - replaces FID
  CLS: { good: 0.1, poor: 0.25 },  // Cumulative Layout Shift (score)
  FCP: { good: 1800, poor: 3000 }, // First Contentful Paint (ms)
  TTFB: { good: 800, poor: 1800 }  // Time to First Byte (ms)
} as const

/**
 * Get performance rating based on metric value
 */
export function getPerformanceRating(
  metricName: keyof typeof PERFORMANCE_THRESHOLDS,
  value: number
): 'good' | 'needs-improvement' | 'poor' {
  const thresholds = PERFORMANCE_THRESHOLDS[metricName]
  if (value <= thresholds.good) return 'good'
  if (value <= thresholds.poor) return 'needs-improvement'
  return 'poor'
}

/**
 * Accurate page load time tracker using Navigation Timing API
 */
export function trackPageLoad() {
  if (typeof window !== 'undefined' && 'performance' in window) {
    window.addEventListener('load', () => {
      // Use modern Navigation Timing API for accurate measurements
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming

      if (navigation) {
        // PerformanceNavigationTiming uses startTime (which is 0) as the reference point
        const loadTime = navigation.loadEventEnd - navigation.startTime
        const domContentLoaded = navigation.domContentLoadedEventEnd - navigation.startTime
        const firstPaint = navigation.responseEnd - navigation.startTime
        const dnsLookup = navigation.domainLookupEnd - navigation.domainLookupStart
        const serverResponse = navigation.responseEnd - navigation.requestStart

        console.log(`[Performance] Page load metrics:`, {
          totalLoadTime: `${Math.round(loadTime)}ms`,
          domContentLoaded: `${Math.round(domContentLoaded)}ms`,
          firstPaint: `${Math.round(firstPaint)}ms`,
          dnsLookup: `${Math.round(dnsLookup)}ms`,
          serverResponse: `${Math.round(serverResponse)}ms`,
          navigationType: navigation.type
        })
      } else {
        // Fallback: use performance.now() which gives time since navigation start
        const loadTime = performance.now()
        console.log(`[Performance] Page load time (fallback): ${Math.round(loadTime)}ms`)
      }
    })
  }
}
