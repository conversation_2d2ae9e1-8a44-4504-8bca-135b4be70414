import Link from "next/link"
import { Phone, MapPin } from "lucide-react"

export default function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-[#4A6C6F] text-white">
      <div className="container mx-auto px-4 py-12 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          {/* Company Info */}
          <div>
            <h3 className="text-xl font-bold mb-4">3S Builder Solution</h3>
            <p className="text-white/80 mb-4">
              Professional yard grading, excavation, land leveling, hardscaping, and landscaping services across Southeastern
              Pennsylvania.
            </p>
            <div className="space-y-2">
              <a href="tel:4132414577" className="flex items-center text-white/80 hover:text-white">
                <Phone className="h-5 w-5 mr-2" />
                <span>************</span>
              </a>

              <div className="flex items-start text-white/80">
                <MapPin className="h-5 w-5 mr-2 mt-1 flex-shrink-0" />
                <span>Serving Southeastern Pennsylvania</span>
              </div>
            </div>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-xl font-bold mb-4">Our Services</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/services/yard-grading" className="text-white/80 hover:text-white">
                  Yard Grading
                </Link>
              </li>
              <li>
                <Link href="/services/excavation" className="text-white/80 hover:text-white">
                  Excavation
                </Link>
              </li>
              <li>
                <Link href="/services/land-leveling" className="text-white/80 hover:text-white">
                  Land Leveling
                </Link>
              </li>
              <li>
                <Link href="/services/hardscaping" className="text-white/80 hover:text-white">
                  Hardscaping
                </Link>
              </li>
              <li>
                <Link href="/services/landscaping" className="text-white/80 hover:text-white">
                  Landscaping
                </Link>
              </li>
            </ul>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-xl font-bold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/about-us" className="text-white/80 hover:text-white">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/locations" className="text-white/80 hover:text-white">
                  Service Areas
                </Link>
              </li>
              <li>
                <Link href="/contact-us" className="text-white/80 hover:text-white">
                  Contact Us
                </Link>
              </li>
            </ul>
          </div>

          {/* Legal */}
          <div>
            <h3 className="text-xl font-bold mb-4">Legal</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/privacy-policy" className="text-white/80 hover:text-white">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="/terms-of-service" className="text-white/80 hover:text-white">
                  Terms of Service
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-12 border-t border-white/30 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-white/80">© {currentYear} 3S Builder Solution. All Rights Reserved.</p>

        </div>
      </div>
    </footer>
  )
}
