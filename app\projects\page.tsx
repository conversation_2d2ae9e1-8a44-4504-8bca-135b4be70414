import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"

export const metadata = {
  title: "Our Projects | 3S Builder Solution",
  description:
    "View our portfolio of completed yard-grading, excavation, land-leveling, hardscaping, and landscaping projects across Southeastern Pennsylvania.",
}

export default function ProjectsPage() {
  return (
    <div className="container mx-auto px-4 py-12 sm:px-6 lg:px-8">
      {/* Hero Section */}
      <div className="text-center mb-12">
        <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">Our Projects</h1>
        <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
          Explore our portfolio of completed projects showcasing our expertise in yard-grading, land-leveling, excavation,
          hardscaping, and landscaping across Southeastern Pennsylvania.
        </p>
      </div>

      {/* Project Categories */}
      <Tabs defaultValue="all" className="w-full mb-12">
        <TabsList className="grid grid-cols-3 md:grid-cols-6 mb-8">
          <TabsTrigger value="all">All Projects</TabsTrigger>
          <TabsTrigger value="yard-grading">Yard Grading</TabsTrigger>
          <TabsTrigger value="excavation">Excavation</TabsTrigger>
          <TabsTrigger value="land-leveling">Land Leveling</TabsTrigger>
          <TabsTrigger value="hardscaping">Hardscaping</TabsTrigger>
          <TabsTrigger value="landscaping">Landscaping</TabsTrigger>
        </TabsList>

        <TabsContent value="all">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "Backyard Transformation",
                location: "Philadelphia, PA",
                description: "Complete backyard renovation including yard-grading, patio installation, and landscaping.",
                image: "/Featured_Projects_in_Philadelphia__Backyard_Transformation_in_Center_City.webp",
                category: "hardscaping",
              },
              {
                title: "Drainage Solution",
                location: "Bensalem, PA",
                description: "Professional yard-grading and drainage system installation to solve persistent water issues.",
                image: "/Featured_Projects_in_Bensalem__Drainage_Solution_in_Bensalem_(1).webp",
                category: "yard-grading",
              },
              {
                title: "Paver Patio Installation",
                location: "Abington, PA",
                description: "Custom paver patio with built-in fire pit and seating walls.",
                image: "/Hardscaping_Services_We_Offer__Patios__Outdoor_Living_Spaces.webp",
                category: "hardscaping",
              },
              {
                title: "Retaining Wall Construction",
                location: "Warminster, PA",
                description: "Engineered retaining wall to create usable space on a sloped property.",
                image: "/Hardscaping_Services_We_Offer__Retaining_Walls.webp",
                category: "hardscaping",
              },
              {
                title: "Landscape Renovation",
                location: "Mount Airy, PA",
                description: "Complete landscape renovation with new plantings, mulch, and decorative stone.",
                image: "/Featured_Projects_in_Mount_Airy__Backyard_Transformation_in_Mount_Airy.webp",
                category: "landscaping",
              },
              {
                title: "Foundation Excavation",
                location: "Horsham, PA",
                description: "Precise excavation for a new home addition foundation.",
                image: "/Types_of_Digging__Excavation_Services_We_Offer__Foundation_Excavation.webp",
                category: "excavation",
              },
              {
                title: "Walkway Installation",
                location: "Willow Grove, PA",
                description: "Natural stone walkway connecting driveway to front entrance.",
                image: "/Hardscaping_Services_We_Offer__Walkways__Paths.webp",
                category: "hardscaping",
              },
              {
                title: "Yard Leveling",
                location: "Southampton, PA",
                description: "Professional land leveling to create a flat, usable outdoor space.",
                image: "/Types_of_land_leveling_Services_We_Offer__Yard_lawn_leveling.webp",
                category: "land-leveling",
              },
              {
                title: "Sod Installation",
                location: "Lansdale, PA",
                description: "Complete lawn renovation with proper yard-grading and sod installation.",
                image: "/Types_of_Landscaping_Services_We_Offer__Sod_Installation.webp",
                category: "landscaping",
              },
            ].map((project, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm overflow-hidden">
                <div className="relative h-64">
                  <Image src={project.image || "/placeholder.svg"} alt={`${project.title} - Professional ${project.category} project by 3S Builder Solution in ${project.location}`} fill className="object-cover" />
                  <div className="absolute top-4 right-4 bg-[#4A6C6F] text-white px-3 py-1 rounded-full text-sm">
                    {project.category.charAt(0).toUpperCase() + project.category.slice(1)}
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-1">{project.title}</h3>
                  <p className="text-[#709CA7] mb-3">{project.location}</p>
                  <p className="text-gray-700 mb-4">{project.description}</p>
                  <Button asChild className="bg-[#4A6C6F] hover:bg-[#3a5a5d]">
                    <Link href={`/projects/${index + 1}`}>View Project Details</Link>
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </TabsContent>

        {/* Other tabs would filter the projects by category */}
        <TabsContent value="yard-grading">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "Drainage Solution",
                location: "Bensalem, PA",
                description: "Professional yard-grading and drainage system installation to solve persistent water issues.",
                image: "/Featured_Projects_in_Bensalem__Drainage_Solution_in_Bensalem_(1).webp",
                category: "yard-grading",
              },
              {
                title: "Slope Correction",
                location: "Chestnut Hill, PA",
                description: "Corrected improper yard-grading to direct water away from the foundation.",
                image: "/Featured_Projects_in_Chestnut_Hill__Drainage_Solution_in_Chestnut_Hill.webp",
                category: "yard-grading",
              },
              {
                title: "New Construction Grading",
                location: "Bristol, PA",
                description: "Precise yard-grading for new home construction to ensure proper drainage.",
                image: "/Types_of_yard_grading_Services_We_Offer__Lot_yard_grading.webp",
                category: "yard-grading",
              },
            ].map((project, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm overflow-hidden">
                <div className="relative h-64">
                  <Image src={project.image || "/placeholder.svg"} alt={`${project.title} - Professional ${project.category} project by 3S Builder Solution in ${project.location}`} fill className="object-cover" />
                  <div className="absolute top-4 right-4 bg-[#4A6C6F] text-white px-3 py-1 rounded-full text-sm">
                    {project.category.charAt(0).toUpperCase() + project.category.slice(1)}
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-1">{project.title}</h3>
                  <p className="text-[#709CA7] mb-3">{project.location}</p>
                  <p className="text-gray-700 mb-4">{project.description}</p>
                  <Button asChild className="bg-[#4A6C6F] hover:bg-[#3a5a5d]">
                    <Link href={`/projects/${index + 1}-yard-yard-grading`}>View Project Details</Link>
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </TabsContent>

        {/* Similar content for other tabs */}
      </Tabs>

      {/* Featured Project */}
      <div className="mb-16">
        <h2 className="text-2xl md:text-3xl font-bold mb-8 text-center">Featured Project</h2>
        <div className="bg-[#F3F1E7] rounded-lg overflow-hidden">
          <div className="grid md:grid-cols-2">
            <div className="relative h-[400px] md:h-auto">
              <Image
                src="/Featured_Project__Complete_Backyard_Transformation.webp"
                alt="Featured project - Complete backyard transformation in Philadelphia, PA showcasing professional yard grading, hardscaping, and landscaping by 3S Builder Solution"
                fill
                className="object-cover"
              />
            </div>
            <div className="p-8 md:p-12">
              <h3 className="text-2xl font-bold mb-2">Complete Backyard Transformation</h3>
              <p className="text-[#709CA7] mb-4">Philadelphia, PA</p>
              <div className="space-y-4 text-gray-700">
                <p>
                  This comprehensive project involved transforming a problematic backyard with drainage issues and
                  uneven terrain into a beautiful, functional outdoor living space.
                </p>
                <p>Our work included:</p>
                <ul className="list-disc pl-5 space-y-2">
                  <li>Professional yard-grading to solve persistent drainage problems</li>
                  <li>Installation of a French drain system to manage water runoff</li>
                  <li>Construction of a 500 sq. ft. paver patio with built-in fire pit</li>
                  <li>Custom stone retaining walls to create level areas</li>
                  <li>Complete landscaping with native plants and trees</li>
                  <li>Installation of landscape lighting for evening enjoyment</li>
                </ul>
                <p>
                  The project was completed in 3 weeks and has completely transformed how the homeowners use their
                  outdoor space.
                </p>
              </div>
              <div className="mt-6">
                <Button asChild className="bg-[#4A6C6F] hover:bg-[#3a5a5d]">
                  <Link href="/projects/featured">View Full Project Details</Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Project Process */}
      <div className="mb-16">
        <h2 className="text-2xl md:text-3xl font-bold mb-8 text-center">Our Project Process</h2>
        <div className="grid md:grid-cols-4 gap-6">
          {[
            {
              step: "1",
              title: "Consultation",
              description:
                "We begin with a thorough consultation to understand your needs, assess your property, and discuss your vision.",
              icon: "M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01",
            },
            {
              step: "2",
              title: "Design & Planning",
              description:
                "We create a detailed plan tailored to your property's specific conditions and your project goals.",
              icon: "M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z",
            },
            {
              step: "3",
              title: "Professional Installation",
              description:
                "Our experienced team executes the plan with attention to every detail, using professional equipment and techniques.",
              icon: "M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z",
            },
            {
              step: "4",
              title: "Final Walkthrough",
              description:
                "We review the completed project together to ensure your complete satisfaction with the results.",
              icon: "M5 13l4 4L19 7",
            },
          ].map((step, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm p-6 border-t-4 border-[#4A6C6F]">
              <div className="inline-flex items-center justify-center h-12 w-12 rounded-full bg-[#709CA7]/20 text-[#4A6C6F] mb-4">
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={step.icon} />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2">
                <span className="text-[#4A6C6F] mr-2">{step.step}.</span>
                {step.title}
              </h3>
              <p className="text-gray-700">{step.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Testimonials */}
      <div className="mb-16 bg-[#F3F1E7] rounded-lg p-8">
        <h2 className="text-2xl md:text-3xl font-bold mb-8 text-center">What Our Clients Say</h2>
        <div className="grid md:grid-cols-3 gap-8">
          {[
            {
              quote:
                "The team transformed our backyard into a beautiful outdoor living space. Their attention to detail and professionalism exceeded our expectations.",
              author: "Michael R.",
              location: "Philadelphia, PA",
              project: "Backyard Transformation",
            },
            {
              quote:
                "The yard grading work they did solved our drainage issues completely. Very knowledgeable team and reasonable pricing.",
              author: "Jennifer T.",
              location: "Bensalem, PA",
              project: "Drainage Solution",
            },
            {
              quote:
                "From the initial consultation to the final walkthrough, working with this team was a pleasure. Highly recommend their hardscaping services!",
              author: "David M.",
              location: "Abington, PA",
              project: "Paver Patio Installation",
            },
          ].map((testimonial, index) => (
            <div key={index} className="p-6 bg-white rounded-lg shadow-sm">
              <div className="flex h-10 w-10 mb-4">
                <svg className="h-10 w-10 text-[#4A6C6F]" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                </svg>
              </div>
              <p className="text-gray-600 italic mb-4">{testimonial.quote}</p>
              <div>
                <p className="font-medium text-gray-900">{testimonial.author}</p>
                <p className="text-gray-500">{testimonial.location}</p>
                <p className="text-[#4A6C6F] mt-1">{testimonial.project}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* CTA */}
      <div className="bg-[#4A6C6F] rounded-lg p-8 text-white text-center">
        <h2 className="text-2xl md:text-3xl font-bold mb-4">Ready to Start Your Project?</h2>
        <p className="text-lg mb-6 max-w-3xl mx-auto">
          Contact 3S Builder Solution today for a free consultation and estimate. Our professional team is ready to help
          transform your outdoor space.
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Button asChild size="lg" className="bg-white text-[#4A6C6F] hover:bg-[#F3F1E7]">
            <Link href="/contact-us">Get a Free Estimate</Link>
          </Button>
          <Button asChild size="lg" className="bg-white text-[#4A6C6F] hover:bg-[#F3F1E7] border-2 border-white">
            <a href="tel:4132414577" className="flex items-center gap-2">
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                />
              </svg>
              <span>************</span>
            </a>
          </Button>
        </div>
      </div>
    </div>
  )
}
