# Deployment Checklist

## Pre-Deployment Checklist

### ✅ Code Quality
- [ ] All TypeScript errors resolved
- [ ] ESLint warnings addressed
- [ ] Build completes successfully (`npm run build`)
- [ ] All pages load correctly in production build
- [ ] No console errors in browser

### ✅ Security
- [ ] Security headers configured in `next.config.mjs`
- [ ] CSP policy allows necessary external resources
- [ ] No sensitive data exposed in client-side code
- [ ] Environment variables properly configured
- [ ] Form submissions working securely

### ✅ Performance
- [ ] Bundle size analyzed (`npm run analyze`)
- [ ] Images optimized (WebP format)
- [ ] Lighthouse scores > 90 for all metrics
- [ ] Core Web Vitals passing
- [ ] No unnecessary dependencies

### ✅ SEO
- [ ] All pages have unique meta titles and descriptions
- [ ] Sitemap generates correctly
- [ ] Robots.txt configured properly
- [ ] Open Graph tags present
- [ ] Structured data implemented

### ✅ Content
- [ ] All contact information updated
- [ ] Service descriptions accurate
- [ ] Location pages complete
- [ ] Privacy policy and terms of service current
- [ ] All images have proper alt text

## Vercel Deployment Steps

### 1. Connect Repository
```bash
# Install Vercel CLI
npm install -g vercel

# Login to Vercel
vercel login

# Link project
vercel link
```

### 2. Configure Environment Variables
In Vercel Dashboard → Settings → Environment Variables:

**Production Variables:**
```
NEXT_PUBLIC_SITE_URL=https://www.3sbuildersolution.com
NEXT_PUBLIC_SITE_NAME=3S Builder Solution
NODE_ENV=production
NEXT_PUBLIC_PHONE=************
NEXT_PUBLIC_EMAIL=<EMAIL>
NEXT_PUBLIC_OPNFORM_ENDPOINT=https://opnform.com/forms/free-estimate-form-afcau9
```

**Optional Analytics Variables:**
```
NEXT_PUBLIC_GA_ID=your-google-analytics-id
NEXT_PUBLIC_GTM_ID=your-google-tag-manager-id
```

### 3. Domain Configuration
1. Add custom domain in Vercel dashboard
2. Configure DNS records:
   - A record: `@` → Vercel IP
   - CNAME record: `www` → `cname.vercel-dns.com`
3. Wait for SSL certificate provisioning

### 4. Deploy
```bash
# Deploy to production
vercel --prod

# Or push to main branch for automatic deployment
git push origin main
```

## Post-Deployment Verification

### ✅ Functionality Tests
- [ ] Homepage loads correctly
- [ ] All navigation links work
- [ ] Contact form submits successfully
- [ ] Service pages display properly
- [ ] Location pages accessible
- [ ] Mobile responsiveness verified

### ✅ Performance Tests
- [ ] Page load times < 3 seconds
- [ ] Lighthouse audit scores > 90
- [ ] Core Web Vitals passing
- [ ] Images loading optimally

### ✅ SEO Verification
- [ ] Google Search Console setup
- [ ] Sitemap submitted to search engines
- [ ] Meta tags displaying correctly in search results
- [ ] Social media sharing working

### ✅ Security Verification
- [ ] HTTPS enforced
- [ ] Security headers present (check with securityheaders.com)
- [ ] No mixed content warnings
- [ ] Form submissions secure

## Monitoring and Maintenance

### Analytics Setup
1. **Google Analytics 4**
   - Add tracking ID to environment variables
   - Verify tracking in GA dashboard

2. **Google Search Console**
   - Verify domain ownership
   - Submit sitemap
   - Monitor search performance

### Performance Monitoring
1. **Vercel Analytics**
   - Enable in Vercel dashboard
   - Monitor Core Web Vitals

2. **Regular Audits**
   - Monthly Lighthouse audits
   - Bundle size monitoring
   - Dependency updates

### Content Updates
1. **Regular Reviews**
   - Update service information
   - Add new project photos
   - Review and update location pages

2. **SEO Maintenance**
   - Monitor search rankings
   - Update meta descriptions
   - Add new content regularly

## Troubleshooting

### Common Issues

**Build Failures:**
- Check TypeScript errors
- Verify all imports are correct
- Ensure environment variables are set

**Performance Issues:**
- Analyze bundle size
- Optimize images
- Check for unnecessary re-renders

**SEO Problems:**
- Verify meta tags
- Check sitemap generation
- Ensure proper URL structure

**Form Issues:**
- Verify OpnForm integration
- Check CSP headers
- Test iframe loading

## Rollback Procedure

If issues occur after deployment:

1. **Immediate Rollback**
   ```bash
   vercel rollback [deployment-url]
   ```

2. **Fix and Redeploy**
   - Identify and fix the issue
   - Test locally
   - Deploy again

3. **Monitor**
   - Check error logs
   - Verify functionality
   - Monitor performance metrics
