import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"

interface CTASectionProps {
  title: string
  description: string
  primaryButtonText?: string
  primaryButtonHref?: string
  showPhoneButton?: boolean
  location?: string
}

export default function CTASection({
  title,
  description,
  primaryButtonText = "Get Your Free Estimate",
  primaryButtonHref = "/contact-us",
  showPhoneButton = true,
  location
}: CTASectionProps) {
  const finalPrimaryText = location 
    ? `${primaryButtonText} in ${location}`
    : primaryButtonText

  return (
    <div className="bg-[#4A6C6F] rounded-lg p-8 text-white text-center">
      <h2 className="text-2xl md:text-3xl font-bold mb-4">{title}</h2>
      <p className="text-lg mb-6 max-w-3xl mx-auto">{description}</p>
      
      <div className="flex flex-col sm:flex-row justify-center gap-4">
        <Button asChild size="lg" className="bg-white text-[#4A6C6F] hover:bg-[#F3F1E7]">
          <Link href={primaryButtonHref}>{finalPrimaryText}</Link>
        </Button>
        
        {showPhoneButton && (
          <Button asChild size="lg" className="bg-white text-[#4A6C6F] hover:bg-[#F3F1E7] border-2 border-white">
            <a href="tel:4132414577" className="flex items-center gap-2">
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                />
              </svg>
              <span>************</span>
            </a>
          </Button>
        )}
      </div>
    </div>
  )
}
